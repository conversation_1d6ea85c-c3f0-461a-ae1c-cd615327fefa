<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraOperasi extends CI_Controller
{
  public function __construct()
  {
    parent::__construct();
    if ($this->session->userdata('logged_in') == false) {
      redirect('login');
    }

    if (!in_array(8, $this->session->userdata('akses'))) {
      redirect('login');
    }

    date_default_timezone_set('Asia/Jakarta');
    $this->load->model(
      [
        'masterModel',
        'pengkajianAwalModel',
        'operasi/PengkajianPraOperasiModel'
      ]
    );
  }

  public function index()
  {
    $nokun = $this->uri->segment(5);
    $getNomr = $this->pengkajianAwalModel->getNomr($nokun);
    $norm = $getNomr['NORM'];

    $data = [
      'getNomr' => $getNomr,
      'riwayatpenggunaanobat' => $this->masterModel->referensi(288),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'kesadaran' => $this->masterModel->referensi(5),
      'persiapandarah' => $this->masterModel->referensi(324),
      'persiapanalatkhusus' => $this->masterModel->referensi(325),
      'historypraoperasi' => $this->pengkajianAwalModel->historypraoperasiRi($norm),
    ];

    // echo'<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/pengkajianPraOperasi', $data);
  }

  public function action_praoperasi($param)
  {
    // echo'<pre>';print_r($param);exit();
    $this->db->trans_begin();
    $post = $this->input->post();
    // echo'<pre>';print_r($post);exit();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
      if ($param == 'tambah' || $param == 'ubah') {
        // Mulai rules
        $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rules());

        if ($param == 'tambah') { // Khusus simpan
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesSimpan());
        }

        if (isset($post['riwayat_guna_obat']) && $post['riwayat_guna_obat'] == '1067') { // Riwayat penggunaan obat
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesPenggunaanObat());
        }

        if (isset($post['alergi']) && $post['alergi'] == '3') { // Riwayat alergi
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesAlergi());
        }

        if (isset($post['psikologis']) && $post['psikologis'] == '38') { // Psikologis
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesPsikologis());
        }

        if (isset($post['persiapan_darah']) && $post['persiapan_darah'] == '1123') { // Persiapan darah
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesPersiapanDarah());
        }

        if (isset($post['persiapan_alatkhusus']) && $post['persiapan_alatkhusus'] == '1125') { // Persiapan alat khusus
          $this->form_validation->set_rules($this->PengkajianPraOperasiModel->rulesPersiapanAlatKhusus());
        }
        // Akhir rules

        if ($this->form_validation->run() == true) {
          $datapraoperasi = [
            'keluhan_utama' => $post['keluhan_utama'] ?? null,
            'riwayat_penyakit_sekarang' => $post['riwayat_penyakit_sekarang'] ?? null,
            'riwayat_guna_obat' => $post['riwayat_guna_obat'] ?? null,
            'desk_riwayat_guna_obat' => $post['desk_riwayat_guna_obat'] ?? null,
            'alergi' => $post['alergi'] ?? null,
            'isi_alergi' => isset($post['isi_alergi']) ? json_encode($post['isi_alergi']) : null,
            'reaksi_alergi' => $post['reaksi_alergi'] ?? null,
            'psikologis' => $post['psikologis'] ?? null,
            'desk_psikologis' => $post['desk_psikologis'] ?? null,
            'hubungan_anggota_keluarga' => $post['hubungan_anggota_keluarga'] ?? null,
            'pencari_nafkah_utama' => $post['pencari_nafkah_utama'] ?? null,
            'tinggal_serumah_dengan' => $post['tinggal_serumah_dengan'] ?? null,
            'kesadaran' => $post['kesadaran'] ?? null,
            'desk_pemeriksaan_fisik' => $post['desk_pemeriksaan_fisik'] ?? null,
            'desk_pemeriksaan_penunjang' => $post['desk_pemeriksaan_penunjang'] ?? null,
            'diagnosis_pra_operasi' => $post['diagnosis_pra_operasi'] ?? null,
            'indikasi_operasi' => $post['indikasi_operasi'] ?? null,
            'rencana_tindakan_operasi' => $post['rencana_tindakan_operasi'] ?? null,
            'persiapan_darah' => $post['persiapan_darah'] ?? null,
            'ket_persiapan_darah' => $post['ket_persiapan_darah'] ?? null,
            'persiapan_alatkhusus' => $post['persiapan_alatkhusus'] ?? null,
            'ket_persiapan_alatkhusus' => $post['ket_persiapan_alatkhusus'] ?? null,
            'tanggal_operasi' => $post['tanggal_operasi'] ?? null,
            'jam_operasi' => $post['jam_operasi'] ?? null,
            'perkiraan_lama_operasi' => $post['perkiraan_lama_operasi'] ?? null,
          ];

          // Mulai aksi
          if (isset($param)) {
            if ($param == 'ubah') {
              // echo '<pre>';print_r($datapraoperasi);exit();
              $this->PengkajianPraOperasiModel->ubah($post['idoperasi'], $datapraoperasi);
            } elseif ($param == 'tambah') {
              $datapraoperasi['nokun'] = $post['nokun'];
              $datapraoperasi['ruang_tujuan'] = $post['ruang_tujuan'];
              $datapraoperasi['oleh'] = $this->session->userdata('id');
              $datapraoperasi['status'] = 1;
              // echo '<pre>';print_r($datapraoperasi);exit();
              $this->PengkajianPraOperasiModel->simpan($datapraoperasi);
            }
          }
          // Akhir aksi

          if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = ['status' => 'failed'];
          } else {
            $this->db->trans_commit();
            $result = ['status' => 'success'];
          }
        } else {
          $result = ['status' => 'failed', 'errors' => $this->form_validation->error_array()];
        }

        echo json_encode($result);
      }
    }
  }

  public function viewPengkajianPraOperasi()
  {
    $idoperasi = $this->input->post('idoperasi');
    $data = [
      'nokun' => $this->input->post('nokun'),
      'idoperasi' => $idoperasi,
      'getpraoperasi' => $this->pengkajianAwalModel->getpraoperasi($idoperasi),
      'riwayatpenggunaanobat' => $this->masterModel->referensi(288),
      'riwayatAlergi' => $this->masterModel->referensi(2),
      'psikologis' => $this->masterModel->referensi(13),
      'sosialDanEkonomiHubungan' => $this->masterModel->referensi(14),
      'sosialDanEkonomiPencariNafkah' => $this->masterModel->referensi(15),
      'sosialDanEkonomiTinggalSerumah' => $this->masterModel->referensi(16),
      'kesadaran' => $this->masterModel->referensi(5),
      'persiapandarah' => $this->masterModel->referensi(324),
      'persiapanalatkhusus' => $this->masterModel->referensi(325),
    ];

    // echo '<pre>';print_r($data);exit();
    $this->load->view('Pengkajian/operasi/editPengkajianPraOperasi', $data);
  }
}

/* End of file PengkajianPraOperasi.php */
/* Location: ./application/controllers/operasi/PengkajianPraOperasi.php */