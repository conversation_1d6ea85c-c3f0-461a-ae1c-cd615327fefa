<?php
defined('BASEPATH') or exit('No direct script access allowed');

class PengkajianPraOperasiModel extends MY_Model
{
    protected $_table = 'medis.tb_pengkajian_operasi';
    protected $_primary_key = 'id';
    protected $_order_by = 'id';
    protected $_order_by_type = 'DESC';

    public function __construct()
    {
        parent::__construct();
    }
    public function rules()
    {
        return [
            [
                'field' => 'keluhan_utama',
                'label' => 'Keluhan utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_penyakit_sekarang',
                'label' => 'Riwayat penyakit sekarang',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'riwayat_guna_obat',
                'label' => 'Riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'alergi',
                'label' => 'Riwayat alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'psikologis',
                'label' => 'Psikologis',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'hubungan_anggota_keluarga',
                'label' => 'Hubungan dengan anggota keluarga',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'pencari_nafkah_utama',
                'label' => 'Pencari nafkah utama',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'tinggal_serumah_dengan',
                'label' => 'Tinggal serumah dengan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'kesadaran',
                'label' => 'Kesadaran',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_fisik',
                'label' => 'Pemeriksaan fisik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'desk_pemeriksaan_penunjang',
                'label' => 'Pemeriksaan penunjang/diagnostik',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'diagnosis_pra_operasi',
                'label' => 'Diagnosis pra operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'indikasi_operasi',
                'label' => 'Indikasi operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'rencana_tindakan_operasi',
                'label' => 'Rencana tindakan operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'perkiraan_lama_operasi',
                'label' => 'Perkiraan lama operasi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_darah',
                'label' => 'Persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
            [
                'field' => 'persiapan_alatkhusus',
                'label' => 'Persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ],
        ];
    }

    public function rulesSimpan()
    {
        return [
            [
                'field' => 'nokun',
                'label' => 'Nomor kunjungan',
                'rules' => 'trim|numeric|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'numeric' => '%s wajib angka',
                ]
            ],
            [
                'field' => 'ruang_tujuan',
                'label' => 'Ruang tujuan',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPenggunaanObat()
    {
        return [
            [
                'field' => 'desk_riwayat_guna_obat',
                'label' => 'Keterangan riwayat penggunaan obat',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesAlergi()
    {
        return [
            [
                'field' => 'isi_alergi[]',
                'label' => 'Sebutkan riwayat alergi',
                'rules' => 'trim|required|max_length[50]',
                'errors' => [
                    'required' => '%s wajib diisi',
                    'max_length' => '%s maksimal 50 karakter',
                ]
            ],
            [
                'field' => 'reaksi_alergi',
                'label' => 'Reaksi alergi',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPsikologis()
    {
        return [
            [
                'field' => 'desk_psikologis',
                'label' => 'Sebutkan psikologis lainnya',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanDarah()
    {
        return [
            [
                'field' => 'ket_persiapan_darah',
                'label' => 'Keterangan persiapan darah',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function rulesPersiapanAlatKhusus()
    {
        return [
            [
                'field' => 'ket_persiapan_alatkhusus',
                'label' => 'Keterangan persiapan alat khusus',
                'rules' => 'trim|required',
                'errors' => [
                    'required' => '%s wajib diisi',
                ]
            ]
        ];
    }

    public function simpan($data)
    {
        $this->db->insert($this->_table, $data);
    }

    public function ubah($id, $data)
    {
        $this->db->where('medis.tb_pengkajian_operasi.id', $id);
        $this->db->update($this->_table, $data);
    }

    public function ambil($nokun)
    {
        $this->db->select('diagnosis_pra_operasi, rencana_tindakan_operasi, perkiraan_lama_operasi');
        $this->db->from($this->_table);
        $this->db->where('nokun', $nokun);
        $this->db->order_by('id', 'desc');
        $query = $this->db->get();
        return $query->row_array();
    }

    /**
     * Method untuk menghitung jumlah operasi berdasarkan tanggal
     * Untuk ruang operasi ID=16 (Operasi Swasta Gedung C)
     */
    public function getJumlahOperasi($tanggal)
    {
        $this->db->select('COUNT(*) as jumlah');
        $this->db->from('remun_medis.perjanjian');
        $this->db->where('ID_RUANGAN', '105090104');
        $this->db->where('STATUS !=', '0');
        $this->db->where('TANGGAL', $tanggal);
        $query = $this->db->get();
        
        $result = $query->row_array();
        return $result['jumlah'] ?? 0;
    }

    /**
     * Method untuk mengecek apakah pasien sudah memiliki pendaftaran operasi pada tanggal tertentu
     * @param string $norm Nomor rekam medis pasien
     * @param string $tanggal Tanggal yang akan dicek
     * @return array|null Return data pasien jika sudah ada pendaftaran, null jika belum ada
     */
    public function cekPendaftaranOperasiByTanggal($norm, $tanggal)
    {
        $this->db->select('a.NOMR, a.TANGGAL, master.getNamaLengkap(a.NOMR) AS nama_pasien');
        $this->db->from('remun_medis.perjanjian a');
        $this->db->where('a.NOMR', $norm);
        $this->db->where('a.TANGGAL', $tanggal);
        $this->db->where('a.RENCANA', '11');
        $this->db->where('a.STATUS !=', '0');
        $query = $this->db->get();
        
        return $query->row_array();
    }

    /**
     * Method untuk mengambil detail operasi dengan server-side processing
     * Mendukung rentang tanggal dan kolom: NO, TANGGAL, WAKTU MULAI, WAKTU SELESAI, TINDAKAN, DOKTER, RUANGAN
     */
    public function getDetailOperasi($tanggal_awal = null, $tanggal_akhir = null, $tanggal = null, $start = 0, $length = 10, $search = '')
    {
        // Base query sesuai permintaan user
        $sql_base = "
            SELECT 
                a.tanggal_operasi,
                a.jam_operasi,
                CONCAT(
                    a.jam_operasi, 
                    ' - ', 
                    DATE_FORMAT(
                        ADDTIME(
                            STR_TO_DATE(a.jam_operasi, '%H:%i'),
                            SEC_TO_TIME(a.perkiraan_lama_operasi * 60)
                        ), 
                        '%H:%i'
                    )
                ) AS waktu,
                a.rencana_tindakan_operasi,
                db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) AS nama_dokter,
                'Instalasi Bedah Gedung C' AS RUANGAN
            FROM medis.tb_pendaftaran_operasi a
            LEFT JOIN remun_medis.perjanjian b
                ON b.ID_PENDAFTARAN_OPERASI = a.id
                AND b.ID_RUANGAN = '105090104'
            WHERE
                b.ID_RUANGAN = '105090104'
                AND a.status != 0
                AND b.STATUS != 0
        ";
        
        $params = [];
        
        // Handle different date parameter scenarios
        if ($tanggal_awal && $tanggal_akhir) {
            // Range mode - untuk kalender 30 hari
            $sql_base .= " AND a.tanggal_operasi BETWEEN ? AND ?";
            $params[] = $tanggal_awal;
            $params[] = $tanggal_akhir;
        } elseif ($tanggal) {
            // Single date mode - untuk tanggal spesifik
            $sql_base .= " AND a.tanggal_operasi = ?";
            $params[] = $tanggal;
        } else {
            // Default: hari ini sampai 30 hari ke depan
            $sql_base .= " AND a.tanggal_operasi BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)";
        }
        
        // Add search condition if provided
        $search_condition = "";
        if (!empty($search)) {
            $search_condition = " AND (
                a.rencana_tindakan_operasi LIKE ? 
                OR db_rekammedis.getNamaLengkapDokter(a.dokter_bedah) LIKE ?
                OR a.jam_operasi LIKE ?
                OR DATE_FORMAT(a.tanggal_operasi, '%d/%m/%Y') LIKE ?
            )";
            $search_param = '%' . $search . '%';
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        // Get total records without pagination for filtered results
        $count_sql = "SELECT COUNT(*) as total FROM (" . $sql_base . $search_condition . ") as count_query";
        $count_query = $this->db->query($count_sql, $params);
        $total_filtered = $count_query->row_array()['total'];

        // Get total records without any filter
        $total_sql = "SELECT COUNT(*) as total FROM (" . $sql_base . ") as total_query";
        $total_query = $this->db->query($total_sql, array_slice($params, 0, count($params) - (empty($search) ? 0 : 4)));
        $total_records = $total_query->row_array()['total'];

        // Add ORDER BY and LIMIT for pagination
        $final_sql = $sql_base . $search_condition . " ORDER BY a.tanggal_operasi ASC, a.jam_operasi ASC LIMIT ?, ?";
        $params[] = (int)$start;
        $params[] = (int)$length;

        // Execute main query
        $query = $this->db->query($final_sql, $params);
        $result = $query->result_array();

        // Process data untuk format DataTables
        $data = [];
        $no = $start + 1;
        foreach ($result as $row) {
            $data[] = [
                'no' => $no++,
                'tanggal' => date('d/m/Y', strtotime($row['tanggal_operasi'])),
                'waktu_mulai' => $row['jam_operasi'] ?? '-',
                'waktu_selesai' => $row['waktu'] ? explode(' - ', $row['waktu'])[1] : '-',
                'tindakan' => $row['rencana_tindakan_operasi'] ?? '-',
                'dokter' => $row['nama_dokter'] ?? '-',
                'ruangan' => $row['RUANGAN']
            ];
        }

        return [
            'data' => $data,
            'recordsTotal' => $total_records,
            'recordsFiltered' => $total_filtered
        ];
    }

    /**
     * Method untuk mengambil data referensi berdasarkan jenis
     * Untuk select2 pada modal lengkapi reservasi
     */
    public function ambilReverensi($jenis, $id)
    {
        $q = $this->input->get('q');
        $this->db->select('JENIS, ID, DESKRIPSI');
        $this->db->from('master.referensi');
        $this->db->where('STATUS', 1);
        $this->db->where('JENIS', $jenis);

        // mulai pencarian
        if ($q) {
            $this->db->like('DESKRIPSI', $q);
        }
        // akhir pencarian

        if ($id) {
            $this->db->where_in('ID', $id);
        }
        
        $this->db->order_by('DESKRIPSI', 'asc');
        $query = $this->db->get();
        return $query->result_array();
    }

    /**
     * Method baru untuk mengambil data kalender operasi 30 hari - DIPERBARUI
     * Menampilkan slot operasi per hari dengan pembagian ruangan
     * Optimized untuk server-side processing
     */
    public function getKalenderOperasi30Hari($tanggal_mulai)
    {
        try {
            // Hitung tanggal akhir (30 hari ke depan)
            $tanggal_akhir = date('Y-m-d', strtotime($tanggal_mulai . ' +30 days'));

            // Query optimized untuk mengambil data operasi dalam rentang 30 hari
            $sql = "
                SELECT
                    a.tanggal_operasi,
                    a.jam_operasi,
                    COALESCE(a.perkiraan_lama_operasi, 120) as perkiraan_lama_operasi,
                    COALESCE(a.diagnosa_medis, 'Tidak ada diagnosa') as diagnosa_medis,
                    COALESCE(a.rencana_tindakan_operasi, 'Tidak ada rencana tindakan') as rencana_tindakan_operasi,
                    CONCAT(COALESCE(c.NAMAPASIEN, 'Nama tidak tersedia'), ' [', COALESCE(c.NOMR, 'N/A'), ']') AS pasien_info,
                    COALESCE(db_rekammedis.getNamaLengkapDokter(a.dokter_bedah), 'Dokter tidak tersedia') AS nama_dokter
                FROM medis.tb_pendaftaran_operasi a
                LEFT JOIN remun_medis.perjanjian b
                    ON b.ID_PENDAFTARAN_OPERASI = a.id
                    AND b.ID_RUANGAN = '105090104'
                LEFT JOIN db_rekammedis.pasien c
                    ON c.NOMR = b.NOMR
                WHERE
                    b.ID_RUANGAN = '105090104'
                    AND a.status != 0
                    AND b.STATUS != 0
                    AND a.tanggal_operasi BETWEEN ? AND ?
                ORDER BY a.tanggal_operasi ASC, a.jam_operasi ASC
            ";

            $query = $this->db->query($sql, [$tanggal_mulai, $tanggal_akhir]);

            if (!$query) {
                log_message('error', 'Database query failed in getKalenderOperasi30Hari: ' . $this->db->error()['message']);
                return [];
            }

            $result = $query->result_array();

            // Organisir data per tanggal untuk 30 hari dengan optimasi memori
            $kalender_data = [];

            // Inisialisasi semua tanggal dalam rentang 30 hari
            for ($i = 0; $i < 30; $i++) {
                $current_date = new DateTime($tanggal_mulai);
                $current_date->add(new DateInterval('P' . $i . 'D'));
                $tanggal_str = $current_date->format('Y-m-d');

                $kalender_data[$tanggal_str] = [
                    'tanggal' => $tanggal_str,
                    'tanggal_format' => $current_date->format('d/m/Y'),
                    'hari' => $this->getHariIndonesia($current_date->format('N')),
                    'operasi' => [],
                    'jumlah_slot_terisi' => 0,
                    'status_penuh' => false
                ];
            }

            // Masukkan data operasi ke tanggal yang sesuai dengan optimasi
            foreach ($result as $row) {
                $tanggal = $row['tanggal_operasi'];
                if (isset($kalender_data[$tanggal])) {
                    $kalender_data[$tanggal]['operasi'][] = [
                        'pasien' => $row['pasien_info'],
                        'dokter' => $row['nama_dokter'],
                        'diagnosa' => $row['diagnosa_medis'],
                        'tindakan' => $row['rencana_tindakan_operasi'],
                        'waktu' => $row['jam_operasi'],
                        'durasi' => $row['perkiraan_lama_operasi']
                    ];
                    $kalender_data[$tanggal]['jumlah_slot_terisi']++;

                    // Tandai sebagai penuh jika sudah 8 slot
                    if ($kalender_data[$tanggal]['jumlah_slot_terisi'] >= 8) {
                        $kalender_data[$tanggal]['status_penuh'] = true;
                    }
                }
            }

            // Return sebagai indexed array untuk konsistensi
            return array_values($kalender_data);

        } catch (Exception $e) {
            log_message('error', 'Exception in getKalenderOperasi30Hari: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Method baru untuk mendapatkan sugesti waktu operasi
     * Berdasarkan slot yang sudah terisi dalam ruangan yang sama
     */
    public function getSugestiWaktuOperasi($tanggal, $ruangan = 1)
    {
        try {
            // Ambil data operasi yang sudah ada pada tanggal tersebut
            $sql = "
                SELECT
                    a.jam_operasi,
                    COALESCE(a.perkiraan_lama_operasi, 120) as perkiraan_lama_operasi
                FROM medis.tb_pendaftaran_operasi a
                LEFT JOIN remun_medis.perjanjian b
                    ON b.ID_PENDAFTARAN_OPERASI = a.id
                    AND b.ID_RUANGAN = '105090104'
                WHERE
                    b.ID_RUANGAN = '105090104'
                    AND a.status != 0
                    AND b.STATUS != 0
                    AND a.tanggal_operasi = ?
                ORDER BY a.jam_operasi ASC
            ";

            $query = $this->db->query($sql, [$tanggal]);

            if (!$query) {
                log_message('error', 'Database query failed in getSugestiWaktuOperasi: ' . $this->db->error()['message']);
                return [
                    'waktu' => '08:00',
                    'keterangan' => 'Waktu default karena terjadi kesalahan'
                ];
            }

            $operasi_existing = $query->result_array();

            // Filter operasi berdasarkan ruangan (distribusi: ruangan 1 = index genap, ruangan 2 = index ganjil)
            $operasi_ruangan = [];
            foreach ($operasi_existing as $index => $operasi) {
                if ($ruangan == 1 && $index % 2 == 0) {
                    $operasi_ruangan[] = $operasi;
                } elseif ($ruangan == 2 && $index % 2 == 1) {
                    $operasi_ruangan[] = $operasi;
                }
            }

            $jumlah_slot = count($operasi_ruangan);

            // Logika sugesti berdasarkan jumlah slot terisi
            if ($jumlah_slot == 0) {
                // Kasus: Tidak ada slot terisi - sarankan jam 08:00
                return [
                    'waktu' => '08:00',
                    'keterangan' => 'Slot pertama di ruangan ' . $ruangan
                ];
            } elseif ($jumlah_slot == 1) {
                // Kasus 1: Hanya 1 slot terisi - hitung berdasarkan jam selesai + 30 menit
                $operasi_pertama = $operasi_ruangan[0];
                $jam_mulai = $operasi_pertama['jam_operasi'];
                $durasi = (int)$operasi_pertama['perkiraan_lama_operasi'];

                // Hitung waktu selesai + 30 menit interval
                $waktu_saran = date('H:i', strtotime($jam_mulai . ' +' . ($durasi + 30) . ' minutes'));

                return [
                    'waktu' => $waktu_saran,
                    'keterangan' => 'Setelah operasi pertama selesai + interval 30 menit'
                ];
            } elseif ($jumlah_slot == 2) {
                // Kasus 2: 2 slot terisi - prioritaskan urutan waktu
                $waktu_tersedia = [];

                foreach ($operasi_ruangan as $operasi) {
                    $jam_mulai = $operasi['jam_operasi'];
                    $durasi = (int)$operasi['perkiraan_lama_operasi'];
                    $waktu_selesai = date('H:i', strtotime($jam_mulai . ' +' . ($durasi + 30) . ' minutes'));
                    $waktu_tersedia[] = $waktu_selesai;
                }

                // Urutkan waktu dan ambil yang paling awal
                sort($waktu_tersedia);

                return [
                    'waktu' => $waktu_tersedia[0],
                    'keterangan' => 'Waktu tersedia paling awal setelah operasi yang ada'
                ];
            } else {
                // Kasus: Lebih dari 2 slot - cari waktu kosong atau sarankan setelah operasi terakhir
                $operasi_terakhir = end($operasi_ruangan);
                $waktu_saran = date('H:i', strtotime($operasi_terakhir['jam_operasi'] . ' +' . ((int)$operasi_terakhir['perkiraan_lama_operasi'] + 30) . ' minutes'));

                return [
                    'waktu' => $waktu_saran,
                    'keterangan' => 'Waktu setelah operasi terakhir + interval 30 menit'
                ];
            }

        } catch (Exception $e) {
            log_message('error', 'Exception in getSugestiWaktuOperasi: ' . $e->getMessage());
            return [
                'waktu' => '08:00',
                'keterangan' => 'Waktu default karena terjadi kesalahan'
            ];
        }
    }

    /**
     * Helper method untuk mendapatkan nama hari dalam bahasa Indonesia
     */
    private function getHariIndonesia($day_number)
    {
        $hari = [
            1 => 'Senin',
            2 => 'Selasa',
            3 => 'Rabu',
            4 => 'Kamis',
            5 => 'Jumat',
            6 => 'Sabtu',
            7 => 'Minggu'
        ];

        return $hari[$day_number] ?? 'Unknown';
    }
}

/* End of file PengkajianPraOperasiModel.php */
/* Location: ./application/models/operasi/PengkajianPraOperasiModel.php */
