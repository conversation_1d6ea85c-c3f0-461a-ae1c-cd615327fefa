<ul class="nav nav-tabs nav-justified">
    <li class="nav-item">
        <a href="#tab-form-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="false" class="nav-link active">
            Form Pendaftaran Pasien Operasi
        </a>
    </li>
    <li class="nav-item">
        <a href="#tab-history-pendaftaran-pra-operasi" data-toggle="tab" aria-expanded="true" class="nav-link">
            History Pendaftaran Pasien Operasi
        </a>
    </li>
</ul>

<div class="tab-content">
    <!-- <PERSON><PERSON> form -->
    <div role="tabpanel" class="tab-pane fade show active" id="tab-form-pendaftaran-pra-operasi">
        <form id="form-pendaftaran-pra-operasi" autocomplete="off">
            <input type="hidden" name="norm" value="<?= $getNomr['NORM'] ?>">
            <input type="hidden" name="dpjp" value="<?= $getNomr['ID_DOKTER'] ?>">
            <input type="hidden" name="nopen" value="<?= $getNomr['NOPEN'] ?>">
            <input type="hidden" name="nokun" value="<?= $getNomr['NOKUN'] ?>">
            <input type="hidden" name="usia" value="<?= $getNomr['USIA'] ?>">
            <input type="hidden" name="ruang_tujuan" value="<?= $getNomr['ID_RUANGAN'] ?>">
            <input type="hidden" name="jenis_kunjungan" value="<?= $getNomr['JENIS_KUNJUNGAN'] ?>">
            <input type="hidden" name="idemr" value="<?= $this->uri->segment(7) ?>">
            <?php if (isset($dataSebelumnya['created_at'])): ?>
                <input type="hidden" name="id_waiting_list" id="id-wl-pendaftaran-pra-operasi" value="<?= $dataSebelumnya['id_waiting_list'] ?? 0 ?>">
            <?php endif ?>
            <!-- Mulai dokter bedah -->
            <div class="row form-group">
                <label for="dokter-bedah-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Dokter Bedah Utama
                </label>
                <div class="col-md-8">
                    <select name="dokter_bedah[]" id="dokter-bedah-pendaftaran-pra-operasi" class="form-control">
                        <option value=""></option>
                        <?php foreach ($listDr as $ld): ?>
                            <option id="dokter-bedah-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>" data-smf="<?= $ld['ID_SMF'] ?>">
                                <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                            </option>
                        <?php endforeach ?>
                    </select>
                </div>
            </div>
            <!-- Akhir dokter bedah -->
            <!-- Mulai rencana tindakan operasi -->
            <div class="row form-group">
                <label for="rencana-tindakan-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Rencana Tindakan Operasi <br>
                    <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                </label>
                <div class="col-md-8">
                    <textarea id="rencana-tindakan-pendaftaran-pra-operasi" name="rencana_tindakan_operasi[]" class="form-control" placeholder="[ Tuliskan Rencana Tindakan ]"><?= $dataPengkajianPraOperasi['rencana_tindakan_operasi'] ?? null ?></textarea>
                </div>
            </div>
            <!-- Akhir rencana tindakan operasi -->
            <!-- Mulai join operasi -->
            <div class="row form-group">
                <label for="join-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    <em>Join</em> Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($joinOperasi as $jo): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="join_operasi" id="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>" class="join-operasi-pendaftaran-pra-operasi" value="<?= $jo['id_variabel'] ?>" <?= $jo['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="join-operasi-pendaftaran-pra-operasi<?= $jo['id_variabel'] ?>">
                                        <?= $jo['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir join operasi -->
            <!-- Mulai form dokter bedah lain -->
            <div class="card new-card form-group" id="form-dokter-bedah-lain-pendaftaran-pra-operasi">
                <div class="card-header new-card-header">Dokter Bedah Lain dan Tindakannya</div>
                <div class="card-body">
                    <!-- Mulai dokter bedah lain -->
                    <div class="row form-group">
                        <label for="dokter-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Dokter Bedah Lain
                        </label>
                        <div class="col-md-8">
                            <select id="dokter-lain-pendaftaran-pra-operasi" class="form-control">
                                <option value=""></option>
                                <?php foreach ($listDr as $ld): ?>
                                    <option id="dokter-lain-pendaftaran-pra-operasi-<?= $ld['ID_DOKTER'] ?>" value="<?= $ld['ID_DOKTER'] ?>">
                                        <?= $ld['DOKTER'] . ' - ' . $ld['SMF'] ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                    <!-- Akhir dokter bedah lain -->
                    <!-- Mulai rencana tindakan dokter bedah lain -->
                    <div class="row form-group">
                        <label for="rencana-tindakan-lain-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                            Rencana Tindakan Operasi untuk Dokter Lain <br>
                            <small>(diisi lengkap dengan bagian atau organ yang akan dioperasi)</small>
                        </label>
                        <div class="col-md-8">
                            <textarea id="rencana-tindakan-lain-pendaftaran-pra-operasi" class="form-control" placeholder="[ Tuliskan Rencana Tindakan untuk Dokter Lain ]"></textarea>
                        </div>
                    </div>
                    <!-- Akhir rencana tindakan dokter bedah lain -->
                    <!-- Mulai aksi dokter bedah lain -->
                    <div class="row form-group">
                        <!-- <div class="col-sm-6">
                            <button type="button" class="btn btn-outline-danger btn-block waves-effect" id="hapus-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Hapus
                            </button>
                        </div> -->
                        <div class="col-sm-12">
                            <button type="button" class="btn btn-outline-success btn-block waves-effect" id="tambah-dokter-bedah-lain-pendaftaran-pra-operasi">
                                Tambah
                            </button>
                        </div>
                    </div>
                    <!-- Akhir aksi dokter bedah lain -->
                    <!-- Mulai tabel dokter bedah lain -->
                    <div class="table-responsive overflow-auto">
                        <table class="table table-bordered table-hover table-custom" cellspacing="0" width="100%">
                            <thead>
                                <tr class="table-tr-custom">
                                    <th>#</th>
                                    <th>Dokter Bedah dan Rencana Tindakan Lain</th>
                                </tr>
                            </thead>
                            <tbody id="list-dokter-lain-pendaftaran-pra-operasi"></tbody>
                        </table>
                    </div>
                    <!-- Akhir tabel dokter bedah lain -->
                </div>
            </div>
            <!-- Akhir form dokter bedah lain -->
            <!-- Mulai diagnosis medis -->
            <div class="row form-group">
                <label for="diagnosa-medis-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Diagnosis Medis
                </label>
                <div class="col-md-8">
                    <input type="text" id="diagnosa-medis-pendaftaran-pra-operasi" name="diagnosa_medis" class="form-control" placeholder="[ Diagnosis Medis ]" value="<?= $dataPengkajianPraOperasi['diagnosis_pra_operasi'] ?? null ?>">
                </div>
            </div>
            <!-- Akhir diagnosis medis -->
              <!-- Mulai penjamin/pembiayaan operasi -->
            <div class="row form-group">
                <label for="ruangan-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Penjamin/Pembiayaan Operasi <span class="text-danger">*</span>
                </label>
                <div class="col-md-8">
                    <select name="ruang_operasi" id="ruangan-operasi-pendaftaran-pra-operasi" class="form-control" required>
                        <option value=""></option>
                        <?php
                        $penjaminOperasi = $this->masterModel->referensiSimpel(81);
                        foreach ($penjaminOperasi as $po):
                            if ($po['ID'] == 2 || $po['ID'] == 16):
                        ?>
                            <option value="<?= $po['ID'] ?>"><?= $po['DESKRIPSI'] ?></option>
                        <?php
                            endif;
                        endforeach;
                        ?>
                    </select>
                </div>
            </div>
            <!-- Akhir penjamin/pembiayaan operasi -->
            <!-- Mulai perkiraan lama operasi -->
            <div class="row form-group">
                <label for="lama-operasi-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Perkiraan Lama Operasi
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="lama-operasi-pendaftaran-pra-operasi" name="perkiraan_lama_operasi" placeholder="[ Lama Operasi dalam Menit ]" value="<?= $dataPengkajianPraOperasi['perkiraan_lama_operasi'] ?? null ?>">
                        <div class="input-group-append">
                            <span class="input-group-text">menit</span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir perkiraan lama operasi -->
            <!-- Mulai tanggal -->
            <div class="row form-group">
                <label for="tanggal-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Tanggal
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="date" class="form-control" id="tanggal-pendaftaran-pra-operasi" name="tanggal_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text" style="cursor: pointer;" id="btn-calendar-modal" title="Lihat jadwal operasi">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                    </div>
                    <!-- Keterangan jumlah operasi id=16 -->
                    <div id="keterangan-jumlah-operasi" class="mt-2" style="display: none;">
                        <span class="text-danger" style="font-size:1em;font-weight:bold;cursor:pointer;" id="link-jumlah-operasi">
                            <i class="fa fa-info-circle"></i> 
                            <span id="text-jumlah-operasi">Jumlah Operasi: 0</span>
                        </span>
                    </div>
                </div>
            </div>
            <!-- Akhir tanggal -->
            <!-- Mulai waktu -->
            <div class="row form-group">
                <label for="waktu-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Waktu
                </label>
                <div class="col-md-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="waktu-pendaftaran-pra-operasi" name="jam_operasi">
                        <div class="input-group-append">
                            <span class="input-group-text"><i class="fa fa-clock"></i></span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Akhir waktu -->
           
            <!-- Mulai tujuan operasi -->
            <div class="row form-group">
                <label for="tujuanOperasiDafOpe" class="col-form-label col-md-4">
                    Tujuan Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($tujuanOperasi as $to): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="tujuan_operasi" id="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>" class="tujuanOperasiDafOpe" value="<?= $to['id_variabel'] ?>" <?= $to['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="tujuanOperasiDafOpe<?= $to['id_variabel'] ?>">
                                        <?= $to['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir tujuan operasi -->
            <!-- Mulai sifat operasi -->
            <div class="row form-group">
                <label for="sifatOperasiDafOpe" class="col-form-label col-md-4">
                    Sifat Operasi
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($sifatOperasi as $so): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="sifat_operasi" id="sifatOperasiDafOpe<?= $so['id_variabel'] ?>" class="sifatOperasiDafOpe" value="<?= $so['id_variabel'] ?>" <?= $so['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="sifatOperasiDafOpe<?= $so['id_variabel'] ?>">
                                        <?= $so['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir sifat operasi -->
            <!-- Mulai alasan CITO -->
            <div class="row form-group d-none" id="form-cito-pendaftaran-pra-operasi">
                <label for="alasan-cito-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan CITO
                </label>
                <div class="col-md-8">
                    <select name="sifat_operasi_lain" id="alasan-cito-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan CITO -->
            <!-- Mulai alasan urgent -->
            <div class="row form-group d-none" id="form-urgent-pendaftaran-pra-operasi">
                <label for="alasan-urgent-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan <em>Urgent</em>
                </label>
                <div class="col-md-8">
                    <select name="alasan_urgent" id="alasan-urgent-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan urgent -->
            <!-- Mulai alasan prioritas -->
            <div class="row form-group d-none" id="form-prioritas-pendaftaran-pra-operasi">
                <label for="alasan-prioritas-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Alasan Prioritas
                </label>
                <div class="col-md-8">
                    <select name="alasan_prioritas" id="alasan-prioritas-pendaftaran-pra-operasi" class="form-controller">
                        <option value=""></option>
                    </select>
                </div>
            </div>
            <!-- Akhir alasan prioritas -->
            <!-- Mulai rencana jenis pembiusan -->
            <div class="row form-group">
                <label for="rencanaJenisPembiusanDafOpe" class="col-form-label col-md-4">
                    Rencana Jenis Pembiusan
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($rencanaJenisPembiusan as $renJenPem): ?>
                            <div class="col-md-6 form-check">
                                <div class="radio radio-primary">
                                    <input type="radio" name="rencana_jenis_pembiusan" id="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>" class="rencanaJenisPembiusanDafOpe" value="<?= $renJenPem['id_variabel'] ?>" <?= $renJenPem['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="rencanaJenisPembiusanDafOpe<?= $renJenPem['id_variabel'] ?>">
                                        <?= $renJenPem['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan -->
            <!-- Mulai rencana jenis pembiusan lain -->
            <div class="row form-group d-none" id="ShowrencanaJenisPembiusan">
                <label for="rencanaJenisPembiusan_lainnya" class="col-form-label col-md-4">
                    Jenis Pembiusan Lainnya
                </label>
                <div class="col-md-8">
                    <textarea id="rencanaJenisPembiusan_lainnya" name="rencana_jenis_pembiusan_lain" class="form-control" placeholder="[ Jelaskan Lainnya ]"></textarea>
                </div>
            </div>
            <!-- Akhir rencana jenis pembiusan lain -->
            <!-- Mulai potong beku -->
            <div class="row form-group">
                <label for="potong-beku-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Potong Beku
                </label>
                <div class="col-md-8">
                    <div class="row pl-1">
                        <?php foreach ($potongBeku as $pb): ?>
                            <div class="col-md form-check form-check-inline">
                                <div class="radio radio-primary">
                                    <input type="radio" name="potong_beku" id="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>" class="potong-beku-pendaftaran-pra-operasi" value="<?= $pb['id_variabel'] ?>" <?= $pb['status_checked'] == 1 ? 'checked' : null ?>>
                                    <label for="potong-beku-pendaftaran-pra-operasi<?= $pb['id_variabel'] ?>">
                                        <?= $pb['variabel'] ?>
                                    </label>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
            <!-- Akhir potong beku -->
            <!-- Mulai catatan khusus -->
            <div class="row form-group">
                <label for="catatan-khusus-pendaftaran-pra-operasi" class="col-form-label col-md-4">
                    Catatan Khusus
                </label>
                <div class="col-md-8">
                    <textarea id="catatan-khusus-pendaftaran-pra-operasi" name="catatan_khusus" class="form-control" placeholder="[ Tuliskan catatan khusus]"></textarea>
                </div>
            </div>
            <!-- Akhir catatan khusus -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="pull-right">
                        <button type="submit" class="btn btn-primary waves-effect" id="simpan-pendaftaran-pra-operasi">
                            <i class="fa fa-save"></i> Simpan
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!-- Akhir form -->
    <!-- Mulai tabel -->
    <div role="tabpanel" class="tab-pane fade" id="tab-history-pendaftaran-pra-operasi">
        <div class="row">
            <div class="col-12">
                <div class="table-responsive">
                    <table id="tabel-pendaftaran-pra-operasi" class="table table-bordered table-hover dt-responsive table-custom" cellspacing="0" width="100%">
                        <thead>
                            <tr class="table-tr-custom">
                                <th>No.</th>
                                <th>Waktu Operasi</th>
                                <th>Jadwal Operasi (OK)</th>
                                <th>Ruang</th>
                                <th>Dokter Bedah Utama</th>
                                <th>Oleh</th>
                                <th>Tanggal Daftar</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            foreach ($historydaftaroperasiReservasi as $hprop):
                            ?>
                                <tr>
                                    <td><?= $no ?>.</td>
                                    <td><?= $hprop['TANGGAL_JAM_OPERASI'] != '0000-00-00, 00:00:00' ? date('d/m/Y, H.i.s', strtotime($hprop['TANGGAL_JAM_OPERASI'])) : '-' ?></td>
                                    <td>
                                        <?php
                                            if (!empty($hprop['TANGGAL_PERJANJIAN']) && $hprop['TANGGAL_PERJANJIAN'] != '0000-00-00 00:00:00') {
                                                echo date('d/m/Y, H:i', strtotime($hprop['TANGGAL_PERJANJIAN']));
                                            } else {
                                                echo '-';
                                            }
                                        ?>
                                    </td>
                                    <td><?= $hprop['RUANGAN'] ?></td>
                                    <td><?= $hprop['DPJP'] ?></td>
                                    <td><?= $hprop['USER'] ?></td>
                                    <td><?= isset($hprop['TANGGAL_DAFTAR']) && $hprop['TANGGAL_DAFTAR'] ? date('d/m/Y, H:i', strtotime($hprop['TANGGAL_DAFTAR'])) : '-' ?> </td>

                                    <td>
                                        <a href="#ubah-pendaftaran-pra-operasi" data-toggle="modal" data-backdrop="static" data-id="<?= $hprop['id'] ?>" data-nokun="<?= $hprop['NOKUN'] ?>" class="btn btn-primary btn-block waves-effect mb-1">
                                            <i class="fas fa-edit"></i> Ubah
                                        </a>
                                        <!-- <pre><?php var_dump($hprop['KODE_BOOKING'] ?? '(kode_booking tidak ada)'); ?></pre> -->
                                        <?php if (!empty($hprop['KODE_BOOKING'])): ?>
                                        <a href="#modal-lengkapi-reservasi" data-toggle="modal" data-backdrop="static" data-id="<?= $hprop['id'] ?>" class="btn btn-success btn-block waves-effect btn-lengkapi-reservasi">
                                            <i class="fas fa-calendar-check"></i> Lengkapi Reservasi
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php
                                $no++;
                            endforeach;
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Akhir tabel -->
</div>

<!-- Mulai modal ubah daftar pra operasi -->
<div aria-hidden="true" aria-labelledby="mySmallModalLabel" class="modal fade" id="ubah-pendaftaran-pra-operasi" role="dialog" style="display: none;">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content" id="hasilDaftarPraOperasi"></div>
    </div>
</div>
<!-- Akhir modal ubah daftar pra operasi -->

<!-- Mulai modal detail operasi harian (untuk tanggal spesifik) -->
<div class="modal fade" id="modal-detail-operasi-harian" tabindex="-1" role="dialog" aria-labelledby="modalDetailOperasiHarianLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailOperasiHarianLabel">
                    <i class="fa fa-calendar-day"></i> Detail Operasi Harian - Gedung C
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            Menampilkan jadwal operasi untuk tanggal <strong id="tanggal-modal-operasi-harian"></strong>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="tabel-detail-operasi-harian" class="table table-bordered table-hover table-striped dt-responsive" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th width="8%">No</th>
                                <th width="12%">Waktu Mulai</th>
                                <th width="12%">Waktu Selesai</th>
                                <th width="35%">Tindakan</th>
                                <th width="23%">Dokter</th>
                                <th width="10%">Ruangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data akan diisi via server-side processing -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal detail operasi harian -->

<!-- Mulai modal detail operasi range (untuk 30 hari) -->
<div class="modal fade" id="modal-detail-operasi-range" tabindex="-1" role="dialog" aria-labelledby="modalDetailOperasiRangeLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalDetailOperasiRangeLabel">
                    <i class="fa fa-calendar-alt"></i> Jadwal Operasi 30 Hari - Gedung C
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 
                            Menampilkan jadwal operasi untuk periode <strong id="tanggal-modal-operasi-range"></strong>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="tabel-detail-operasi-range" class="table table-bordered table-hover table-striped dt-responsive" cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th width="6%">No</th>
                                <th width="12%">Tanggal</th>
                                <th width="10%">Waktu Mulai</th>
                                <th width="10%">Waktu Selesai</th>
                                <th width="35%">Tindakan</th>
                                <th width="18%">Dokter</th>
                                <th width="9%">Ruangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data akan diisi via server-side processing -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal detail operasi range -->

<!-- Mulai modal lengkapi reservasi -->
<div class="modal fade" id="modal-lengkapi-reservasi" tabindex="-1" role="dialog" aria-labelledby="modalLengkapiReservasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalLengkapiReservasiLabel">
                    <i class="fa fa-calendar-check"></i> Lengkapi Reservasi Operasi
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="form-lengkapi-reservasi">
                    <input type="hidden" id="reservasi-id-pendaftaran" name="id_pendaftaran">
                    <input type="hidden" id="reservasi-id-reservasi" name="id_reservasi">
                    
                     <!-- Data Editable -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-edit"></i> Data Reservasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Cara Bayar <span class="text-danger">*</span></label>
                                        <select id="reservasi-cara-bayar" class="form-control" required>
                                            <option value="">[ Pilih Cara Bayar ]</option>
                                            <?php foreach($caraBayarOptions as $cb): ?>
                                                <option value="<?= $cb['ID'] ?>"><?= $cb['DESKRIPSI'] ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Kelas Rawat <span class="text-danger">*</span></label>
                                        <select id="reservasi-kelas-rawat" class="form-control" required>
                                            <option value="">[ Pilih Kelas Rawat ]</option>
                                            <?php foreach($kelasRawatOptions as $kr): ?>
                                                <option value="<?= $kr['ID'] ?>"><?= $kr['DESKRIPSI'] ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Data Readonly -->
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fa fa-info-circle"></i> Informasi Pasien & Operasi</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Nomor RM & Nama Pasien</label>
                                        <input type="text" id="reservasi-norm-nama" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Dokter</label>
                                        <input type="text" id="reservasi-dokter" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Kode Booking</label>
                                        <input type="text" id="reservasi-kode-booking" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">Tanggal Operasi</label>
                                        <input type="text" id="reservasi-tanggal-operasi" class="form-control" readonly>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Diagnosis</label>
                                        <textarea id="reservasi-diagnosis" class="form-control" rows="2" readonly></textarea>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Tindakan Operasi</label>
                                        <textarea id="reservasi-tindakan" class="form-control" rows="2" readonly></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                   
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir modal lengkapi reservasi -->

<!-- Modal Kalender Operasi 30 Hari - BARU -->
<div class="modal fade" id="modal-kalender-operasi-30hari" tabindex="-1" role="dialog" aria-labelledby="modalKalenderOperasi30HariLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-dark text-white">
                <h5 class="modal-title" id="modalKalenderOperasi30HariLabel">
                    <i class="fa fa-calendar"></i> List Perjanjian Operasi
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body bg-dark text-white">
                <!-- Header Navigasi Bulan -->
                <div class="row mb-3">
                    <div class="col-md-12 text-center">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-light" id="btn-prev-month-30hari">
                                <i class="fa fa-chevron-left"></i>
                            </button>
                            <button type="button" class="btn btn-outline-light" id="current-month-year-30hari">
                                Juli 2025
                            </button>
                            <button type="button" class="btn btn-outline-light" id="btn-next-month-30hari">
                                <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Container Kalender -->
                <div id="kalender-container-30hari">
                    <!-- Kalender akan dirender di sini via JavaScript -->
                </div>
            </div>
            <div class="modal-footer bg-dark">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir Modal Kalender Operasi 30 Hari -->

<!-- Modal Detail Slot Operasi - BARU -->
<div class="modal fade" id="modal-detail-slot-operasi" tabindex="-1" role="dialog" aria-labelledby="modalDetailSlotOperasiLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="modalDetailSlotOperasiLabel">
                    <i class="fa fa-calendar-day"></i> Detail Slot Operasi - <span id="detail-slot-tanggal-30hari"></span>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Tabs untuk Ruangan -->
                <ul class="nav nav-tabs" id="ruangan-tabs-30hari" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="ruangan1-tab-30hari" data-toggle="tab" href="#ruangan1-30hari" role="tab">
                            <i class="fa fa-hospital"></i> Ruangan 1
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="ruangan2-tab-30hari" data-toggle="tab" href="#ruangan2-30hari" role="tab">
                            <i class="fa fa-hospital"></i> Ruangan 2
                        </a>
                    </li>
                </ul>

                <!-- Tab Content -->
                <div class="tab-content mt-3" id="ruangan-tabs-content-30hari">
                    <!-- Ruangan 1 -->
                    <div class="tab-pane fade show active" id="ruangan1-30hari" role="tabpanel">
                        <div class="row" id="ruangan-1-slots-30hari">
                            <!-- Slots akan dirender di sini -->
                        </div>
                        <div class="mt-3">
                            <label for="sugesti-waktu-ruangan1-30hari">Sugesti Waktu untuk Slot Kosong:</label>
                            <div class="input-group">
                                <input type="time" class="form-control" id="sugesti-waktu-ruangan1-30hari" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-primary" type="button" id="btn-pilih-slot-ruangan1-30hari">
                                        <i class="fa fa-check"></i> Pilih Slot Ini
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted" id="keterangan-sugesti-ruangan1-30hari"></small>
                        </div>
                    </div>

                    <!-- Ruangan 2 -->
                    <div class="tab-pane fade" id="ruangan2-30hari" role="tabpanel">
                        <div class="row" id="ruangan-2-slots-30hari">
                            <!-- Slots akan dirender di sini -->
                        </div>
                        <div class="mt-3">
                            <label for="sugesti-waktu-ruangan2-30hari">Sugesti Waktu untuk Slot Kosong:</label>
                            <div class="input-group">
                                <input type="time" class="form-control" id="sugesti-waktu-ruangan2-30hari" readonly>
                                <div class="input-group-append">
                                    <button class="btn btn-primary" type="button" id="btn-pilih-slot-ruangan2-30hari">
                                        <i class="fa fa-check"></i> Pilih Slot Ini
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted" id="keterangan-sugesti-ruangan2-30hari"></small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fa fa-times"></i> Tutup
                </button>
            </div>
        </div>
    </div>
</div>
<!-- Akhir Modal Detail Slot Operasi -->

<!-- Include Toastr CSS dan JS -->
<!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css" rel="stylesheet">
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.js"></script> -->

<!-- CSS Kalender Operasi 30 Hari - BARU -->
<style>
    /* Kalender Container */
    #kalender-container-30hari {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 10px;
        padding: 15px;
        background-color: #2c3e50;
        border-radius: 8px;
    }

    /* Header Hari */
    .kalender-header-hari {
        text-align: center;
        font-weight: bold;
        padding: 10px;
        background-color: #34495e;
        color: #ecf0f1;
        border-radius: 5px;
        font-size: 14px;
    }

    /* Kotak Tanggal */
    .kalender-tanggal-box {
        background-color: #34495e;
        border: 2px solid #4a5f7a;
        border-radius: 8px;
        padding: 8px;
        min-height: 120px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .kalender-tanggal-box:hover {
        border-color: #3498db;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(52, 152, 219, 0.3);
    }

    /* Status Penuh - Merah */
    .kalender-tanggal-box.penuh {
        background-color: #e74c3c !important;
        border-color: #c0392b !important;
    }

    .kalender-tanggal-box.penuh:hover {
        border-color: #a93226 !important;
        box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
    }

    /* Nomor Tanggal */
    .kalender-nomor-tanggal {
        font-size: 16px;
        font-weight: bold;
        color: #ecf0f1;
        margin-bottom: 5px;
    }

    /* Nama Hari */
    .kalender-nama-hari {
        font-size: 11px;
        color: #bdc3c7;
        margin-bottom: 8px;
    }

    /* Slot Counter */
    .kalender-slot-counter {
        position: absolute;
        top: 5px;
        right: 8px;
        background-color: #3498db;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
    }

    .kalender-slot-counter.penuh {
        background-color: #fff !important;
        color: #e74c3c !important;
    }

    /* Slot Operasi Mini */
    .kalender-slot-mini {
        background-color: #27ae60;
        color: white;
        padding: 2px 4px;
        margin: 1px 0;
        border-radius: 3px;
        font-size: 9px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Slot Detail dalam Modal */
    .slot-operasi-card {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .slot-operasi-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }

    .slot-operasi-card.kosong {
        background-color: #e9ecef;
        border-style: dashed;
        text-align: center;
        color: #6c757d;
    }

    .slot-operasi-card.terisi {
        background-color: #d4edda;
        border-color: #c3e6cb;
    }

    /* Info dalam slot */
    .slot-info-item {
        margin-bottom: 8px;
        font-size: 13px;
    }

    .slot-info-label {
        font-weight: bold;
        color: #495057;
        display: inline-block;
        width: 80px;
    }

    .slot-info-value {
        color: #212529;
    }

    /* Responsive untuk mobile */
    @media (max-width: 768px) {
        #kalender-container-30hari {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            padding: 10px;
        }

        .kalender-tanggal-box {
            min-height: 100px;
            padding: 6px;
        }

        .kalender-nomor-tanggal {
            font-size: 14px;
        }

        .kalender-nama-hari {
            font-size: 10px;
        }
    }

    /* Loading state */
    .kalender-loading {
        text-align: center;
        padding: 40px;
        color: #ecf0f1;
    }

    .kalender-loading i {
        font-size: 24px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>

<script>
    // Konfigurasi toastr global
    toastr.options = {
        closeButton: true,
        progressBar: true,
        showDuration: 300,
        hideDuration: 1000,
        extendedTimeOut: 1000,
        positionClass: 'toast-top-right'
    };
    $(document).ready(function() {
        // Mulai history
        $('#tabel-pendaftaran-pra-operasi').DataTable({
            responsive: true,
            language: {
                'sEmptyTable': 'Maaf, tidak ada data yang tersedia',
                'sInfo': 'Menampilkan _START_ sampai _END_ dari _TOTAL_ data',
                'sInfoEmpty': 'Menampilkan 0 sampai 0 dari 0 data',
                'sInfoFiltered': '(Pencarian dari _MAX_ total data)',
                'sInfoPostFix': '',
                'sInfoThousands': ',',
                'sLengthMenu': 'Menampilkan _MENU_ data',
                'sLoadingRecords': 'Harap tunggu...',
                'sProcessing': 'Sedang memproses...',
                'sSearch': 'Pencarian:',
                'sZeroRecords': 'Data tidak ditemukan',
                'oPaginate': {
                    'sFirst': 'Pertama',
                    'sLast': 'Terakhir',
                    'sNext': 'Selanjutnya',
                    'sPrevious': 'Sebelumnya'
                }
            },
        });
        // Akhir history

        // Mulai dokter bedah
        let smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        $('#dokter-bedah-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih ]'
        }).val("<?= $getNomr['ID_DOKTER'] ?? 0 ?>").trigger('change');

        $('#dokter-bedah-pendaftaran-pra-operasi').change(function() {
            smf = $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf');
        });
        // Akhir dokter bedah

        // Mulai dokter bedah lain
        $('#dokter-lain-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih dokter bedah lain ]'
        });
        // Akhir dokter bedah lain

        // Mulai penjamin/pembiayaan operasi
        $('#ruangan-operasi-pendaftaran-pra-operasi').select2({
            placeholder: '[ Pilih penjamin/pembiayaan ]'
        });

        // Auto-select penjamin/pembiayaan operasi berdasarkan RUANGAN/GEDUNG pasien
        var id_ruangan_pasien = "<?= $getNomr['ID_RUANGAN'] ?>";

        // Jika ID_RUANGAN ada
        if (id_ruangan_pasien) {
            // Panggil AJAX untuk mendapatkan detail ruangan
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/cekRuangan') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    id_ruangan: id_ruangan_pasien
                },
                success: function(response) {
                    if (response) {
                        // Jika GEDUNG is NULL, pilih Operasi Reguler (ID=2)
                        if (response.GEDUNG === null) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('2').trigger('change');
                        }
                        // Jika GEDUNG == 1, pilih Operasi Swasta (Gedung C) (ID=16)
                        else if (response.GEDUNG == 1) {
                            $('#ruangan-operasi-pendaftaran-pra-operasi').val('16').trigger('change');
                        }
                    }
                }
            });
        }
        // Akhir ruangan operasi

        // Fungsi untuk menambah dokter bedah lain
        function tambahDokterBedahLain() {
            let jumlah = $('.hapus-item-dokter-lain').length;
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let dokter = $('#dokter-lain-pendaftaran-pra-operasi option:selected').text().trim();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val();
            let isi = null;

            // Mulai periksa
            if (jumlah <= 4) { // Periksa jumlah apakah sudah ada 5
                if (id_dokter !== '' && tindakan !== '') { // Periksa apakah sudah diisi
                    // Mulai isi
                    isi = '<tr>' +
                        '<td class="text-center">' +
                        "<button type='button' class='btn btn-danger btn-sm hapus-item-dokter-lain' title='Hapus'><i class='fa fa-xmark'></i></button>" +
                        "<input type='hidden' class='isi-id-dokter-lain-pendaftaran-pra-operasi' name='dokter_bedah[]' value='" + id_dokter + "'>" +
                        '</td>' +
                        '<td>' +
                        "<div class='form-group'><input type='text' class='form-control isi-dokter-lain-pendaftaran-pra-operasi' value='" + dokter + "' aria-label='Dokter Bedah Lain' readonly></div>" +
                        "<div><textarea class='form-control isi-rencana-tindakan-pendaftaran-pra-operasi' name='rencana_tindakan_operasi[]' aria-label='Rencana Tindakan Operasi untuk Dokter Lain' readonly>" + tindakan + "</textarea></div>" +
                        '</td>' +
                        '</tr>';
                    $(isi).hide().appendTo('#list-dokter-lain-pendaftaran-pra-operasi').fadeIn(1000);
                    // Akhir isi

                    // Mulai bersihkan form`
                    $('#dokter-lain-pendaftaran-pra-operasi').val(0).trigger('change');
                    $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val(null);
                    // Akhri bersihkan form
                    return true;
                } else {
                    return false;
                }
            } else {
                toastr.warning('Sudah ada 5 dokter bedah lain', 'Peringatan', {
                    timeOut: 4000
                });
                return false;
            }
            // Akhir periksa
        }

        // Mulai tambah dokter bedah lain (manual button click)
        $('#tambah-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            if (!tambahDokterBedahLain()) {
                toastr.warning('Mohon isi data dokter bedah lain dan tindakannya lebih dulu', 'Peringatan', {
                    timeOut: 4000
                });
            }
        });
        // Akhir tambah dokter bedah lain

        // Auto-add yang lebih user-friendly - dipicu ketika user pindah ke field lain atau melakukan action
        // HANYA akan auto-add jika KEDUA field sudah terisi (dokter DAN rencana tindakan)
        function checkAutoAdd() {
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            
            // Validasi ketat: KEDUA field harus terisi
            if (id_dokter !== '' && id_dokter !== null && tindakan !== '' && tindakan.length > 0) {
                tambahDokterBedahLain();
            }
        }

        // Event listener untuk auto-add - hanya pada action tertentu, bukan saat mengetik
        $('#rencana-tindakan-lain-pendaftaran-pra-operasi').on('blur', function() {
            // Auto-add ketika user keluar dari textarea (blur) - tapi hanya jika dokter juga sudah dipilih
            let id_dokter = $('#dokter-lain-pendaftaran-pra-operasi').val();
            if (id_dokter !== '' && id_dokter !== null) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user mengubah pilihan dokter - tapi hanya jika rencana tindakan juga sudah diisi
        $('#dokter-lain-pendaftaran-pra-operasi').on('change', function() {
            let tindakan = $('#rencana-tindakan-lain-pendaftaran-pra-operasi').val().trim();
            if (tindakan !== '' && tindakan.length > 0) {
                checkAutoAdd();
            }
        });

        // Auto-add ketika user klik field lain setelah mengisi KEDUA field
        $('#diagnosa-medis-pendaftaran-pra-operasi, #lama-operasi-pendaftaran-pra-operasi, #tanggal-pendaftaran-pra-operasi, #waktu-pendaftaran-pra-operasi, #ruangan-operasi-pendaftaran-pra-operasi').on('focus', function() {
            // Auto-add ketika user klik field lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Auto-add ketika user klik radio button tujuan operasi, sifat operasi, dll
        $('.tujuanOperasiDafOpe, .sifatOperasiDafOpe, .rencanaJenisPembiusanDafOpe, .potong-beku-pendaftaran-pra-operasi').on('click', function() {
            // Auto-add ketika user klik radio button lain - tapi hanya jika KEDUA field sudah terisi
            checkAutoAdd();
        });

        // Event listener untuk hapus individual dengan icon trash
        $(document).on('click', '.hapus-item-dokter-lain', function() {
            $(this).closest('tr').fadeOut(500, function() {
                $(this).remove();
            });
        });

        // Hapus bulk (tidak digunakan lagi, tapi tetap dipertahankan untuk kompatibilitas)
        $('#hapus-dokter-bedah-lain-pendaftaran-pra-operasi').click(function() {
            $('#list-dokter-lain-pendaftaran-pra-operasi').find($('.pilih-dokter-lain-pendaftaran-pra-operasi')).each(function() {
                if ($(this).is(':checked')) {
                    $(this).parents('tr').fadeOut(500, function() {
                        $(this).remove();
                    });
                }
            });
        });
        // Akhir hapus dokter bedah lain
        //coba
          $('#waktu-pendaftaran-pra-operasi').timepicker({
            showMeridian: false,    
            minuteStep: 1,
            defaultTime: 'current', 
            showInputs: true        
        });
        $('#lama-operasi-pendaftaran-pra-operasi').on('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        //akhir coba

        // Mulai sifat operasi
        $('.sifatOperasiDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2131') { // CITO
                $('#form-cito-pendaftaran-pra-operasi').removeClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6080') { // Urgent
                $('#form-cito-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#form-urgent-pendaftaran-pra-operasi').removeClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            } else if (id === '6125') { // Prioritas
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi').val(null).trigger('change');
                $('#form-prioritas-pendaftaran-pra-operasi').removeClass('d-none');
            } else { // Elektif
                $('#form-cito-pendaftaran-pra-operasi, #form-urgent-pendaftaran-pra-operasi, #form-prioritas-pendaftaran-pra-operasi').addClass('d-none');
                $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').val(null).trigger('change');
            }

            // Mulai alasan sifat operasi
            $('#alasan-cito-pendaftaran-pra-operasi, #alasan-urgent-pendaftaran-pra-operasi, #alasan-prioritas-pendaftaran-pra-operasi').select2({
                placeholder: '[ Pilih alasan ]',
                ajax: {
                    url: "<?= base_url('operasi/PengkajianDafOpe/alasan') ?>",
                    dataType: 'json',
                    delay: 250,
                    method: 'POST',
                    data: {
                        sifatOperasi: id,
                        smf: $('#dokter-bedah-pendaftaran-pra-operasi').find(':selected').data('smf')
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                }
            }).val(0).trigger('change');
            // Akhir alasan sifat operasi
        });
        // Akhir sifat operasi

        // Mulai rencana jenis pembiusan
        $('.rencanaJenisPembiusanDafOpe').click(function() {
            let id = $(this).val();
            if (id === '2138') {
                $('#ShowrencanaJenisPembiusan').removeClass('d-none');
            } else {
                $('#ShowrencanaJenisPembiusan').addClass('d-none');
                $('#rencanaJenisPembiusan_lainnya').val(null);
            }
        });

        if ($('.rencanaJenisPembiusanDafOpe:checked').val() === '2138') {
            $('#ShowrencanaJenisPembiusan').removeClass('d-none');
        }
        // Akhir rencana jenis pembiusan

        // Mulai join operasi
        $('.join-operasi-pendaftaran-pra-operasi').click(function() {
            let joinOperasiValue = $(this).val();
            if (joinOperasiValue === '6249') { // Ya
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
            } else { // Tidak
                $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
            }
        });

        // Inisialisasi visibility form dokter bedah lain berdasarkan nilai yang sudah dipilih
        let selectedJoinOperasi = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
        if (selectedJoinOperasi === '6249') {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').show();
        } else {
            $('#form-dokter-bedah-lain-pendaftaran-pra-operasi').hide();
        }
        // Akhir join operasi

        // Mulai fitur cek jumlah operasi untuk ruang operasi ID=16
        function cekJumlahOperasi() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            
            // Hanya tampilkan untuk ruang operasi ID=16 dan tanggal sudah diisi
            if (ruangOperasi === '16' && tanggal !== '') {
                // Tampilkan loading indicator
                $('#text-jumlah-operasi').html('<i class="fa fa-spinner fa-spin"></i> Mengecek jumlah operasi...');
                $('#keterangan-jumlah-operasi').show();
                
                // AJAX call ke controller
                $.ajax({
                    url: "<?= base_url('operasi/PengkajianDafOpe/cekJumlahOperasi') ?>",
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        tanggal: tanggal
                    },
                    success: function(response) {
                        if (response) {
                            $('#text-jumlah-operasi').html('Jumlah Operasi: ' + response.jumlah);
                        } else {
                            $('#text-jumlah-operasi').html('Jumlah Operasi: 0');
                        }
                    },
                    error: function() {
                        $('#text-jumlah-operasi').html('Jumlah Operasi: Error');
                    }
                });
            } else {
                // Sembunyikan keterangan jika bukan ruang operasi ID=16 atau tanggal kosong
                $('#keterangan-jumlah-operasi').hide();
            }
        }

        // Event listener untuk perubahan ruang operasi
        $('#ruangan-operasi-pendaftaran-pra-operasi').on('change', function() {
            cekJumlahOperasi();
        });

        // Event listener untuk perubahan tanggal
        $('#tanggal-pendaftaran-pra-operasi').on('change', function() {
            cekJumlahOperasi();
            cekValidasiTanggalOperasi();
        });
        // Akhir fitur cek jumlah operasi

        // Mulai fitur validasi tanggal operasi
        function cekValidasiTanggalOperasi() {
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            let norm = "<?= $getNomr['NORM'] ?>";
            let ruang_operasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            
            // Debug log untuk melihat parameter yang akan dikirim
            console.log('cekValidasiTanggalOperasi - norm:', norm, 'tanggal:', tanggal, 'ruang_operasi:', ruang_operasi);
            
            // Hanya lakukan validasi jika ruang operasi ID=16
            if (ruang_operasi === '16' && tanggal !== '' && norm !== '') {
                // AJAX call ke controller untuk cek validasi
                $.ajax({
                    url: "<?= base_url('operasi/PengkajianDafOpe/cekPendaftaranOperasiTanggal') ?>",
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        norm: norm,
                        tanggal: tanggal,
                        ruang_operasi: ruang_operasi
                    },
                    success: function(response) {
                        console.log('Response validasi:', response);
                        if (response.status === 'sudah_ada') {
                            // Tampilkan notifikasi error
                            toastr.error(response.message, 'Error', {
                                timeOut: 6000
                            });
                            
                            // Reset tanggal ke null agar user bisa pilih ulang
                            $('#tanggal-pendaftaran-pra-operasi').val('');
                            
                            // Sembunyikan keterangan jumlah operasi jika ada
                            $('#keterangan-jumlah-operasi').hide();
                        } else if (response.status === 'aman') {
                            // Tanggal aman, bisa dilanjutkan
                            console.log('Tanggal aman untuk pendaftaran operasi');
                        } else if (response.status === 'error') {
                            toastr.warning(response.message, 'Peringatan', {
                                timeOut: 4000
                            });
                            console.log('Error detail:', response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        toastr.error('Terjadi kesalahan saat validasi tanggal operasi', 'Error', {
                            timeOut: 6000
                        });
                        console.log('AJAX Error:', xhr.responseText, status, error);
                    }
                });
            } else if (ruang_operasi !== '16') {
                // Jika bukan ruang operasi ID=16, tidak perlu validasi
                console.log('Validasi diabaikan karena bukan ruang operasi IBS C');
            }
        }
        // Akhir fitur validasi tanggal operasi

        // Mulai fitur modal detail operasi dengan server-side processing (TERPISAH)
        let detailOperasiHarianTable = null;
        let detailOperasiRangeTable = null;
        
        // Event listener untuk klik pada teks jumlah operasi (Modal Harian - tanggal spesifik)
        $(document).on('click', '#link-jumlah-operasi', function() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            let tanggal = $('#tanggal-pendaftaran-pra-operasi').val();
            
            // Hanya tampilkan modal jika ruang operasi ID=16 dan tanggal sudah diisi
            if (ruangOperasi === '16' && tanggal !== '') {
                // Format tanggal untuk ditampilkan di modal
                let tanggalFormat = new Date(tanggal).toLocaleDateString('id-ID', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                $('#tanggal-modal-operasi-harian').text(tanggalFormat);
                
                // Tampilkan modal harian
                $('#modal-detail-operasi-harian').modal('show');
                
                // Inisialisasi DataTables harian
                if (detailOperasiHarianTable) {
                    detailOperasiHarianTable.destroy();
                }
                
                detailOperasiHarianTable = $('#tabel-detail-operasi-harian').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: {
                        url: "<?= base_url('operasi/PengkajianDafOpe/getDetailOperasi') ?>",
                        type: 'POST',
                        data: function(d) {
                            // Kirim parameter tanggal spesifik saja
                            d.tanggal = tanggal;
                        }
                    },
                    columns: [
                        { 
                            data: 'no',
                            name: 'no',
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        },
                        { 
                            data: 'waktu_mulai',
                            name: 'waktu_mulai',
                            className: 'text-center'
                        },
                        { 
                            data: 'waktu_selesai',
                            name: 'waktu_selesai',
                            className: 'text-center'
                        },
                        { 
                            data: 'tindakan',
                            name: 'tindakan'
                        },
                        { 
                            data: 'dokter',
                            name: 'dokter'
                        },
                        { 
                            data: 'ruangan',
                            name: 'ruangan',
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        }
                    ],
                    order: [[1, 'asc']], // Sort by waktu_mulai
                    pageLength: 10,
                    lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
                    language: {
                        'sEmptyTable': 'Tidak ada data operasi untuk tanggal ini',
                        'sInfo': 'Menampilkan _START_ sampai _END_ dari _TOTAL_ operasi',
                        'sInfoEmpty': 'Menampilkan 0 sampai 0 dari 0 operasi',
                        'sInfoFiltered': '(Pencarian dari _MAX_ total operasi)',
                        'sInfoPostFix': '',
                        'sInfoThousands': ',',
                        'sLengthMenu': 'Menampilkan _MENU_ operasi per halaman',
                        'sLoadingRecords': 'Harap tunggu...',
                        'sProcessing': 'Sedang memproses...',
                        'sSearch': 'Pencarian:',
                        'sZeroRecords': 'Tidak ada operasi yang sesuai dengan pencarian',
                        'oPaginate': {
                            'sFirst': 'Pertama',
                            'sLast': 'Terakhir',
                            'sNext': 'Selanjutnya',
                            'sPrevious': 'Sebelumnya'
                        }
                    },
                    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                         '<"row"<"col-sm-12"tr>>' +
                         '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    scrollX: true,
                    autoWidth: false
                });
            }
        });
        
        // Event listener untuk destroy DataTable modal harian
        $('#modal-detail-operasi-harian').on('hidden.bs.modal', function() {
            if (detailOperasiHarianTable) {
                detailOperasiHarianTable.destroy();
                detailOperasiHarianTable = null;
            }
        });

        // Event listener untuk klik ikon kalender (Modal Kalender 30 hari) - DIPERBARUI
        $('#btn-calendar-modal').on('click', function() {
            let ruangOperasi = $('#ruangan-operasi-pendaftaran-pra-operasi').val();
            if (ruangOperasi === '16') {
                // Tampilkan modal kalender 30 hari untuk Gedung C
                $('#modal-kalender-operasi-30hari').modal('show');
                loadKalenderOperasi30Hari();
            } else {
                toastr.warning('Kalender operasi hanya tersedia untuk Operasi Swasta (Gedung C)', 'Peringatan', {
                    timeOut: 4000
                });
            }
        });

        // Fungsi untuk memuat kalender operasi 30 hari - BARU
        function loadKalenderOperasi30Hari(tanggalMulai = null) {
            // Tampilkan loading
            $('#kalender-container-30hari').html(`
                <div class="kalender-loading col-12">
                    <i class="fa fa-spinner fa-spin"></i>
                    <p class="mt-2">Memuat kalender operasi...</p>
                </div>
            `);

            // Set tanggal mulai (default: hari ini)
            if (!tanggalMulai) {
                tanggalMulai = new Date().toISOString().split('T')[0];
            }

            // Update header bulan
            const tanggalObj = new Date(tanggalMulai);
            const namaBulan = tanggalObj.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' });
            $('#current-month-year-30hari').text(namaBulan);

            // AJAX request
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/getKalenderOperasi30Hari') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    tanggal_mulai: tanggalMulai
                },
                success: function(response) {
                    if (response.status === 'success') {
                        renderKalenderOperasi30Hari(response.data);
                    } else {
                        $('#kalender-container-30hari').html(`
                            <div class="col-12 text-center text-danger">
                                <i class="fa fa-exclamation-triangle"></i>
                                <p class="mt-2">Gagal memuat data: ${response.message}</p>
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading calendar:', error);
                    $('#kalender-container-30hari').html(`
                        <div class="col-12 text-center text-danger">
                            <i class="fa fa-exclamation-triangle"></i>
                            <p class="mt-2">Terjadi kesalahan saat memuat kalender</p>
                        </div>
                    `);
                }
            });
        }

        // Fungsi untuk render kalender - BARU
        function renderKalenderOperasi30Hari(data) {
            let kalenderHtml = '';

            // Header hari
            const namaHari = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
            namaHari.forEach(hari => {
                kalenderHtml += `<div class="kalender-header-hari">${hari}</div>`;
            });

            // Data tanggal
            data.forEach(item => {
                const tanggalObj = new Date(item.tanggal);
                const nomorTanggal = tanggalObj.getDate();
                const namaHariSingkat = item.hari.substring(0, 3);

                // Tentukan class berdasarkan status
                let boxClass = 'kalender-tanggal-box';
                let slotCounterClass = 'kalender-slot-counter';

                if (item.status_penuh) {
                    boxClass += ' penuh';
                    slotCounterClass += ' penuh';
                }

                // Buat mini slots
                let miniSlots = '';
                item.operasi.slice(0, 4).forEach(operasi => {
                    const waktuSingkat = operasi.waktu.substring(0, 5);
                    miniSlots += `<div class="kalender-slot-mini" title="${operasi.pasien}">${waktuSingkat}</div>`;
                });

                kalenderHtml += `
                    <div class="${boxClass}" data-tanggal="${item.tanggal}" onclick="showDetailSlotOperasi('${item.tanggal}')">
                        <div class="kalender-nomor-tanggal">${nomorTanggal}</div>
                        <div class="kalender-nama-hari">${namaHariSingkat}</div>
                        <div class="${slotCounterClass}">${item.jumlah_slot_terisi}/8</div>
                        <div class="kalender-mini-slots">
                            ${miniSlots}
                            ${item.jumlah_slot_terisi > 4 ? '<div class="kalender-slot-mini">...</div>' : ''}
                        </div>
                    </div>
                `;
            });

            $('#kalender-container-30hari').html(kalenderHtml);
        }

        // Fungsi untuk menampilkan detail slot operasi - BARU
        function showDetailSlotOperasi(tanggal) {
            // Set tanggal di header modal
            const tanggalObj = new Date(tanggal);
            const tanggalFormat = tanggalObj.toLocaleDateString('id-ID', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            $('#detail-slot-tanggal-30hari').text(tanggalFormat);

            // Tampilkan modal
            $('#modal-detail-slot-operasi').modal('show');

            // Load detail slots untuk kedua ruangan
            loadDetailSlotRuangan(tanggal, 1);
            loadDetailSlotRuangan(tanggal, 2);
        }

        // Fungsi untuk load detail slot per ruangan - BARU
        function loadDetailSlotRuangan(tanggal, ruangan) {
            const containerId = `#ruangan-${ruangan}-slots-30hari`;

            // Tampilkan loading
            $(containerId).html(`
                <div class="col-12 text-center">
                    <i class="fa fa-spinner fa-spin"></i>
                    <p class="mt-2">Memuat slot ruangan ${ruangan}...</p>
                </div>
            `);

            // AJAX untuk mendapatkan data operasi pada tanggal tersebut
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/getKalenderOperasi30Hari') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    tanggal_mulai: tanggal
                },
                success: function(response) {
                    if (response.status === 'success' && response.data.length > 0) {
                        const dataHari = response.data[0];
                        renderSlotRuangan(dataHari.operasi, ruangan, tanggal);
                        loadSugestiWaktu(tanggal, ruangan);
                    } else {
                        renderSlotRuangan([], ruangan, tanggal);
                        loadSugestiWaktu(tanggal, ruangan);
                    }
                },
                error: function() {
                    $(containerId).html(`
                        <div class="col-12 text-center text-danger">
                            <p>Gagal memuat data slot ruangan ${ruangan}</p>
                        </div>
                    `);
                }
            });
        }

        // Fungsi untuk render slot per ruangan - BARU
        function renderSlotRuangan(operasiData, ruangan, tanggal) {
            const containerId = `#ruangan-${ruangan}-slots-30hari`;
            let slotsHtml = '';

            // Filter operasi berdasarkan ruangan (distribusi: ruangan 1 = index genap, ruangan 2 = index ganjil)
            const operasiRuangan = operasiData.filter((operasi, index) => {
                return ruangan == 1 ? index % 2 == 0 : index % 2 == 1;
            });

            // Render 4 slot untuk ruangan ini
            for (let i = 0; i < 4; i++) {
                const operasi = operasiRuangan[i];

                if (operasi) {
                    // Slot terisi
                    slotsHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="slot-operasi-card terisi">
                                <div class="slot-info-item">
                                    <span class="slot-info-label">Pasien:</span>
                                    <span class="slot-info-value">${operasi.pasien}</span>
                                </div>
                                <div class="slot-info-item">
                                    <span class="slot-info-label">Dokter:</span>
                                    <span class="slot-info-value">${operasi.dokter}</span>
                                </div>
                                <div class="slot-info-item">
                                    <span class="slot-info-label">Waktu:</span>
                                    <span class="slot-info-value">${operasi.waktu}</span>
                                </div>
                                <div class="slot-info-item">
                                    <span class="slot-info-label">Tindakan:</span>
                                    <span class="slot-info-value">${operasi.tindakan}</span>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // Slot kosong
                    slotsHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="slot-operasi-card kosong">
                                <i class="fa fa-plus-circle fa-2x mb-2"></i>
                                <p class="mb-0">Slot Kosong</p>
                                <small>Tersedia untuk operasi</small>
                            </div>
                        </div>
                    `;
                }
            }

            $(containerId).html(slotsHtml);
        }

        // Fungsi untuk load sugesti waktu - BARU
        function loadSugestiWaktu(tanggal, ruangan) {
            $.ajax({
                url: "<?= base_url('operasi/PengkajianDafOpe/getSugestiWaktuOperasi') ?>",
                method: 'POST',
                dataType: 'json',
                data: {
                    tanggal: tanggal,
                    ruangan: ruangan
                },
                success: function(response) {
                    if (response.status === 'success') {
                        $(`#sugesti-waktu-ruangan${ruangan}-30hari`).val(response.sugesti_waktu);
                        $(`#keterangan-sugesti-ruangan${ruangan}-30hari`).text(response.keterangan);
                    }
                },
                error: function() {
                    $(`#sugesti-waktu-ruangan${ruangan}-30hari`).val('08:00');
                    $(`#keterangan-sugesti-ruangan${ruangan}-30hari`).text('Waktu default');
                }
            });
        }

        // Event listener untuk pilih slot - BARU
        $(document).on('click', '#btn-pilih-slot-ruangan1-30hari, #btn-pilih-slot-ruangan2-30hari', function() {
            const ruangan = $(this).attr('id').includes('ruangan1') ? 1 : 2;
            const waktu = $(`#sugesti-waktu-ruangan${ruangan}-30hari`).val();
            const tanggal = $('#detail-slot-tanggal-30hari').text();

            // Set nilai ke form utama
            const tanggalISO = new Date($('.kalender-tanggal-box.active').data('tanggal')).toISOString().split('T')[0];
            $('#tanggal-pendaftaran-pra-operasi').val(tanggalISO);
            $('#waktu-pendaftaran-pra-operasi').val(waktu);

            // Tutup modal
            $('#modal-detail-slot-operasi').modal('hide');
            $('#modal-kalender-operasi-30hari').modal('hide');

            toastr.success(`Slot dipilih: ${tanggal} pada ${waktu} (Ruangan ${ruangan})`, 'Berhasil');
        });

        // Event listener untuk destroy DataTable modal range
        $('#modal-detail-operasi-range').on('hidden.bs.modal', function() {
            if (detailOperasiRangeTable) {
                detailOperasiRangeTable.destroy();
                detailOperasiRangeTable = null;
            }
        });
        
        // Akhir fitur modal detail operasi

        // Mulai simpan daftar operasi dengan validasi
        $('#form-pendaftaran-pra-operasi').submit(function(event) {
            event.preventDefault();
            
            // Validasi join operasi
            let joinOperasiValue = $('.join-operasi-pendaftaran-pra-operasi:checked').val();
            let jumlahDokterLain = $('.hapus-item-dokter-lain').length;
            
            // Jika pilih "Ya" tapi list dokter bedah lain kosong
            if (joinOperasiValue === '6249' && jumlahDokterLain === 0) {
                toastr.warning('Dokter Bedah Lain dan Tindakannya harus diisi karena Anda memilih "Ya" pada Join Operasi', 'Peringatan', {
                    timeOut: 4000
                });
                return false;
            }
            
            let dataDAF_OPERASI = $(this).serializeArray();

            $.ajax({
                dataType: 'json',
                url: "<?= base_url('operasi/PengkajianDafOpe/action_dafoperasi/tambah') ?>",
                method: 'POST',
                data: dataDAF_OPERASI,
                success: function(data) {
                    if (data.status === 'success') {
                        toastr.success('Data tersimpan', 'Berhasil', {
                            timeOut: 3000
                        });
                        location.reload();
                    } else {
                        $.each(data.errors, function(index, element) {
                            toastr.warning(element, 'Peringatan', {
                                timeOut: 4000
                            });
                        });
                    }
                }
            });
        });
        // Akhir simpan daftar operasi

        // Mulai tampil daftar pra operasi
        $('#ubah-pendaftaran-pra-operasi').on('show.bs.modal', function(e) {
            let id = $(e.relatedTarget).data('id');
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/viewDaftarPraOperasi') ?>",
                data: {
                    id: id
                },
                success: function(data) {
                    $('#hasilDaftarPraOperasi').html(data);
                }
            });
            // e.preventDefault();
        });
        // Akhir tampil daftar pra operasi

        // Mulai lengkapi reservasi
        // Variable untuk tracking nilai awal select2
        let initialCaraBayar = null;
        let initialKelasRawat = null;
        
        // Inisialisasi select2 untuk cara bayar dan kelas rawat dengan data dari backend
        $('#reservasi-cara-bayar').select2({
            placeholder: '[ Pilih Cara Bayar ]',
            dropdownParent: $('#modal-lengkapi-reservasi'),
            // allowClear: true
        });

        $('#reservasi-kelas-rawat').select2({
            placeholder: '[ Pilih Kelas Rawat ]',
            dropdownParent: $('#modal-lengkapi-reservasi'),
            // allowClear: true
        });
        
        // Event listener untuk modal lengkapi reservasi
        $('#modal-lengkapi-reservasi').on('show.bs.modal', function(e) {
            let id = $(e.relatedTarget).data('id');
            
            // Reset form dan initial values
            $('#form-lengkapi-reservasi')[0].reset();
            initialCaraBayar = null;
            initialKelasRawat = null;
            
            // Load data reservasi
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/getDataReservasi') ?>",
                data: { id: id },
                dataType: 'json',
                success: function(data) {
                    if (data.status === 'success') {
                        let res = data.data;
                        
                        // Isi field readonly
                        $('#reservasi-id-pendaftaran').val(res.id_pendaftaran);
                        $('#reservasi-id-reservasi').val(res.id_reservasi);
                        $('#reservasi-norm-nama').val(res.norm + ' [' + res.nama_pasien + ']');
                        $('#reservasi-dokter').val(res.nama_dokter);
                        $('#reservasi-kode-booking').val(res.kode_booking);
                        $('#reservasi-tanggal-operasi').val(res.tanggal_operasi);
                        $('#reservasi-diagnosis').val(res.diagnosis);
                        $('#reservasi-tindakan').val(res.tindakan);
                        
                        // Set nilai awal untuk tracking
                        initialCaraBayar = res.id_cara_bayar || null;
                        initialKelasRawat = res.id_kelas || null;
                        
                        // Set selected value langsung tanpa AJAX - data sudah tersedia di backend
                        if (res.id_cara_bayar) {
                            $('#reservasi-cara-bayar').val(res.id_cara_bayar).trigger('change');
                        }
                        if (res.id_kelas) {
                            $('#reservasi-kelas-rawat').val(res.id_kelas).trigger('change');
                        }
                    } else {
                        toastr.error(data.message || 'Gagal memuat data reservasi', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat memuat data reservasi', 'Error');
                }
            });
        });

        // Auto-update saat select berubah (dengan validasi perubahan)
        $('#reservasi-cara-bayar').on('change', function() {
            let currentValue = $(this).val() || null;
            
            // Hanya update jika value berubah dari nilai awal
            if (currentValue !== initialCaraBayar) {
                updateReservasi('id_cara_bayar', currentValue);
                initialCaraBayar = currentValue; // Update nilai tracking
            }
        });

        $('#reservasi-kelas-rawat').on('change', function() {
            let currentValue = $(this).val() || null;
            
            // Hanya update jika value berubah dari nilai awal
            if (currentValue !== initialKelasRawat) {
                updateReservasi('id_kelas', currentValue);
                initialKelasRawat = currentValue; // Update nilai tracking
            }
        });

        // Fungsi untuk update reservasi (dengan validasi tambahan)
        function updateReservasi(field, value) {
            let id_reservasi = $('#reservasi-id-reservasi').val();
            
            if (!id_reservasi) {
                toastr.warning('ID Reservasi tidak ditemukan', 'Peringatan');
                return;
            }
            
            // Tampilkan loading indicator
            let fieldLabel = (field === 'id_cara_bayar') ? 'Cara Bayar' : 'Kelas Rawat';
            toastr.info('Menyimpan ' + fieldLabel + '...', 'Proses', {
                timeOut: 1000
            });
            
            $.ajax({
                type: 'POST',
                url: "<?= base_url('operasi/PengkajianDafOpe/updateReservasi') ?>",
                data: {
                    id_reservasi: id_reservasi,
                    field: field,
                    value: value
                },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        toastr.success(response.message, 'Berhasil', {
                            timeOut: 2000
                        });
                    } else {
                        toastr.error(response.message || 'Gagal update data reservasi', 'Error');
                    }
                },
                error: function() {
                    toastr.error('Terjadi kesalahan saat update data reservasi', 'Error');
                }
            });
        }
        // Akhir lengkapi reservasi
    });
</script>
